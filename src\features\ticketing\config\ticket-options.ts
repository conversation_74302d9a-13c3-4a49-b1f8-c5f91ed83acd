import { TicketPriority, Department } from '../models/ticket.schema';

type OptionConfig<T extends string> = {
  [key in T]: {
    label: string;
    color: string;
    dotColor: string;
  };
};

export const priorityConfig: OptionConfig<TicketPriority> = {
  low: {
    label: 'Low Priority',
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100',
    dotColor: 'bg-gray-500',
  },
  medium: {
    label: 'Medium Priority',
    color:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
    dotColor: 'bg-yellow-500',
  },
  high: {
    label: 'High Priority',
    color: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
    dotColor: 'bg-red-500',
  },
  urgent: {
    label: 'Urgent Priority',
    color: 'bg-red-200 text-red-900 dark:bg-red-700 dark:text-red-100',
    dotColor: 'bg-red-700',
  },
};

// Department configuration removed - use useDepartmentColors hook for database-driven colors
// This ensures all department colors come from the database configuration

export const statusColors: { [key: string]: string } = {
  new: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  open: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  closed: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  pending:
    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  resolved: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
};

// Utility functions for consistent formatting (DRY principle)
export const formatTicketStatus = (status: string) =>
  status.charAt(0).toUpperCase() + status.slice(1);

export const formatTicketPriority = (priority: string) =>
  `${priority.charAt(0).toUpperCase() + priority.slice(1)} Priority`;

export const formatTicketDepartment = (department: string) =>
  `${department.charAt(0).toUpperCase() + department.slice(1)} Department`;

// Utility for deduplication (SOLID principle - single responsibility)
export const deduplicateTickets = <T extends { id: string }>(items: T[]): T[] =>
  items.filter(
    (item, index, arr) => arr.findIndex((t) => t.id === item.id) === index
  );
