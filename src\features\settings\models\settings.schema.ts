import { z } from 'zod';

// Theme preference schema
export const ThemePreferenceSchema = z.enum(['light', 'dark', 'system']);

// User preferences schema
export const UserPreferencesSchema = z
  .object({
    notifications: z
      .object({
        email: z.boolean().default(true),
        browser: z.boolean().default(true),
        sound: z.boolean().default(false),
      })
      .default(() => ({
        email: true,
        browser: true,
        sound: false,
      })),
    language: z.string().default('en'),
    timezone: z.string().default('UTC'),
    dateFormat: z
      .enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])
      .default('MM/DD/YYYY'),
    timeFormat: z.enum(['12h', '24h']).default('12h'),
  })
  .default(() => ({
    notifications: {
      email: true,
      browser: true,
      sound: false,
    },
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY' as const,
    timeFormat: '12h' as const,
  }));

// User settings schema
export const UserSettingsSchema = z.object({
  id: z.string().uuid().optional(),
  user_id: z.string().uuid(),
  tenant_id: z.string().uuid(),
  theme_preference: ThemePreferenceSchema.default('system'),
  preferences: UserPreferencesSchema,
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

// User settings update schema (for API requests)
export const UserSettingsUpdateSchema = z.object({
  theme_preference: ThemePreferenceSchema.optional(),
  preferences: z
    .object({
      notifications: z
        .object({
          email: z.boolean().optional(),
          browser: z.boolean().optional(),
          sound: z.boolean().optional(),
        })
        .optional(),
      language: z.string().optional(),
      timezone: z.string().optional(),
      dateFormat: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']).optional(),
      timeFormat: z.enum(['12h', '24h']).optional(),
    })
    .optional(),
});

// Default agent settings schema
export const DefaultAgentSettingsSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  default_agent_id: z.string().uuid().nullable(),
  is_active: z.boolean().default(true),
  created_by: z.string().uuid(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

// Auto assignment rule schema
export const AutoAssignmentRuleSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  department: z.enum(['sales', 'support', 'marketing', 'technical']),
  assigned_agent_id: z.string().uuid().nullable(),
  is_default: z.boolean().default(false),
  priority: z.number().int().min(1).max(100).default(1),
  is_active: z.boolean().default(true),
  created_by: z.string().uuid(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

// Auto assignment rules update schema (for API requests)
export const AutoAssignmentRulesUpdateSchema = z.array(
  z.object({
    id: z.string().uuid().optional(),
    department: z.enum(['sales', 'support', 'marketing', 'technical']),
    assigned_agent_id: z.string().uuid().nullable(),
    is_default: z.boolean().optional(),
    priority: z.number().int().min(1).max(100).optional(),
    is_active: z.boolean().optional(),
  })
);

// Admin settings schema
export const AdminSettingsSchema = z.object({
  default_agent_settings: DefaultAgentSettingsSchema.nullable(),
  auto_assignment_rules: z.array(AutoAssignmentRuleSchema).default([]),
});

// Admin settings update schema (for API requests)
export const AdminSettingsUpdateSchema = z.object({
  default_agent_id: z.string().uuid().nullable().optional(),
  default_agent_is_active: z.boolean().optional(),
  auto_assignment_rules: AutoAssignmentRulesUpdateSchema.optional(),
});

// Complete settings response schema
export const SettingsResponseSchema = z.object({
  user_settings: UserSettingsSchema,
  admin_settings: AdminSettingsSchema.optional(),
});

// Avatar upload schema
export const AvatarUploadSchema = z.object({
  avatar_url: z.string().url(),
  file_name: z.string(),
});

// Password change schema
export const PasswordChangeSchema = z
  .object({
    current_password: z.string().min(8, 'Current password is required'),
    new_password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one lowercase letter, one uppercase letter, and one number'
      ),
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

// Settings form data schema (for frontend forms)
export const SettingsFormSchema = z.object({
  // User profile settings
  theme_preference: ThemePreferenceSchema,
  preferences: z
    .object({
      notifications: z
        .object({
          email: z.boolean().optional(),
          browser: z.boolean().optional(),
          sound: z.boolean().optional(),
        })
        .optional(),
      language: z.string().optional(),
      timezone: z.string().optional(),
      dateFormat: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']).optional(),
      timeFormat: z.enum(['12h', '24h']).optional(),
    })
    .optional(),

  // Admin settings (optional, only for admins)
  default_agent_id: z.string().uuid().nullable().optional(),
  auto_assignment_rules: z
    .array(
      z.object({
        department: z.enum(['sales', 'support', 'marketing', 'technical']),
        assigned_agent_id: z.string().uuid().nullable(),
        is_active: z.boolean().default(true),
      })
    )
    .optional(),
});

// Export TypeScript types
export type ThemePreference = z.infer<typeof ThemePreferenceSchema>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type UserSettings = z.infer<typeof UserSettingsSchema>;
export type UserSettingsUpdate = z.infer<typeof UserSettingsUpdateSchema>;
export type DefaultAgentSettings = z.infer<typeof DefaultAgentSettingsSchema>;
export type AutoAssignmentRule = z.infer<typeof AutoAssignmentRuleSchema>;
export type AutoAssignmentRulesUpdate = z.infer<
  typeof AutoAssignmentRulesUpdateSchema
>;
export type AdminSettings = z.infer<typeof AdminSettingsSchema>;
export type AdminSettingsUpdate = z.infer<typeof AdminSettingsUpdateSchema>;
export type SettingsResponse = z.infer<typeof SettingsResponseSchema>;
export type AvatarUpload = z.infer<typeof AvatarUploadSchema>;
export type PasswordChange = z.infer<typeof PasswordChangeSchema>;
export type SettingsForm = z.infer<typeof SettingsFormSchema>;

// Settings validation helpers
export const validateUserSettings = (data: unknown) => {
  return UserSettingsUpdateSchema.safeParse(data);
};

export const validateAdminSettings = (data: unknown) => {
  return AdminSettingsUpdateSchema.safeParse(data);
};

export const validatePasswordChange = (data: unknown) => {
  return PasswordChangeSchema.safeParse(data);
};

export const validateSettingsForm = (data: unknown) => {
  return SettingsFormSchema.safeParse(data);
};

// Default values
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  notifications: {
    email: true,
    browser: true,
    sound: false,
  },
  language: 'en',
  timezone: 'UTC',
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h',
};

export const DEFAULT_USER_SETTINGS: Partial<UserSettings> = {
  theme_preference: 'system',
  preferences: DEFAULT_USER_PREFERENCES,
};

// Department configuration removed - use useDepartmentColors hook for database-driven colors
// This ensures all department colors come from the database configuration
